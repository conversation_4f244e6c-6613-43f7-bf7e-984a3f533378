"""
CT Image IOD Template Implementation.

Provides a complete DICOM Information Object Definition (IOD) template for CT
images, implementing all required and optional modules according to DICOM Part 3
Section C.8.2.1 (CT Image IOD).

## Clinical Usage

The CT Image template ensures that all created CT DICOM files contain the proper
DICOM elements required for clinical viewing systems and treatment planning
systems. This includes mandatory elements for image geometry, patient positioning,
and acquisition parameters.

```python
import pyrt_dicom as prt
import numpy as np

# Create CT from array data
ct_array = np.random.randint(-1000, 3000, (512, 512, 200), dtype=np.int16)
ct_series = prt.CTSeries.from_array(
    ct_array,
    pixel_spacing=(1.0, 1.0),  # mm
    slice_thickness=2.5,       # mm
    patient_position='HFS'     # Head First Supine
)
ct_series.save('ct_series.dcm')
```

## DICOM Compliance

This template implements the complete CT Image IOD as defined in DICOM PS 3.3
Part 3, ensuring compatibility with all major DICOM viewers and treatment
planning systems including:

- Varian Eclipse
- Elekta Monaco  
- RaySearch RayStation
- MiM Software
- 3D Slicer
- DICOM viewers (OsiriX, Horos, etc.)

## Cross-References

**Related Classes**:
- :class:`~pyrt_dicom.core.ct_series.CTSeries` - CT series creation implementation
- :class:`~pyrt_dicom.core.base.BaseDicomCreator` - Base DICOM creator framework
- :class:`~pyrt_dicom.coordinates.transforms.CoordinateTransformer` - Geometric transformations

**See Also**:
- DICOM Part 3 Section C.8.2.1: CT Image IOD
- DICOM Part 6: Data Dictionary for element definitions
- :mod:`~pyrt_dicom.validation.dicom_compliance` - IOD compliance validation
"""

from typing import Dict, Any, Optional
import pydicom
from pydicom.dataset import Dataset
from pydicom.uid import CTImageStorage
import numpy as np

from ..utils.exceptions import TemplateError


class CTImageTemplate:
    """DICOM CT Image IOD template implementation.
    
    Creates properly structured DICOM datasets conforming to the CT Image IOD
    specification (DICOM PS 3.3 C.8.2.1). Handles all required modules and
    ensures clinical compatibility across TPS vendors.
    
    Clinical Notes:
        The CT Image IOD is fundamental to radiotherapy workflows as it provides
        the geometric reference frame for all subsequent RT objects. Key clinical
        requirements include:
        
        - Sub-millimeter geometric accuracy for dose calculation
        - Proper Hounsfield Unit scaling for tissue density calculations
        - Consistent patient positioning for treatment delivery
        - Frame of Reference UID consistency across RT objects
        
        Image position and orientation elements are critical for maintaining
        spatial relationships in treatment planning systems.
    
    Attributes:
        SOP_CLASS_UID: CT Image Storage SOP Class UID (1.2.840.10008.*******.1.2)
        REQUIRED_MODULES: List of mandatory DICOM modules for CT Image IOD
        OPTIONAL_MODULES: List of optional modules that enhance clinical functionality
    """
    
    # CT Image Storage SOP Class UID from DICOM Part 6
    SOP_CLASS_UID = CTImageStorage
    
    # Required modules per DICOM Part 3 C.8.2.1
    REQUIRED_MODULES = [
        'Patient',
        'General Study', 
        'General Series',
        'Frame of Reference',
        'General Equipment',
        'General Image',
        'Image Plane',
        'Image Pixel',
        'CT Image',
        'SOP Common'
    ]
    
    # Optional modules that enhance clinical functionality
    OPTIONAL_MODULES = [
        'Multi-frame',
        'Contrast/Bolus',
        'Device', 
        'Specimen',
        'Clinical Trial Subject',
        'Clinical Trial Study',
        'Clinical Trial Series'
    ]
    
    @classmethod
    def create_dataset(
        self,
        pixel_array: np.ndarray,
        pixel_spacing: tuple[float, float],
        slice_thickness: float,
        image_position: tuple[float, float, float],
        image_orientation: tuple[float, ...] = (1.0, 0.0, 0.0, 0.0, 1.0, 0.0),
        rescale_intercept: float = -1024.0,
        rescale_slope: float = 1.0,
        **kwargs
    ) -> Dataset:
        """Create CT Image dataset with proper IOD structure.
        
        Args:
            pixel_array: 2D CT image data as numpy array. Should contain Hounsfield
                Units for proper clinical interpretation. Typical range: -1000 to +3000 HU.
            pixel_spacing: Physical pixel spacing in mm as (row_spacing, col_spacing).
                Typical clinical values: 0.5-2.0mm for high resolution planning CTs.
            slice_thickness: Slice thickness in mm. Common values: 1.0-5.0mm for RT
                planning, with 1.25-2.5mm preferred for small target treatments.
            image_position: Physical position of first pixel in mm as (x, y, z) in 
                DICOM patient coordinate system. Establishes spatial reference frame.
            image_orientation: Direction cosines for row and column directions as
                6-element tuple. Default (1,0,0,0,1,0) represents standard axial
                orientation (patient HFS, no gantry tilt).
            rescale_intercept: Intercept for converting pixel values to Hounsfield Units.
                Standard CT value is -1024 (air at -1000 HU maps to pixel value 0).
            rescale_slope: Slope for HU conversion. Standard value is 1.0 for direct
                mapping of pixel values to Hounsfield Units.
            **kwargs: Additional DICOM elements to include in dataset.
                
        Returns:
            Complete DICOM dataset conforming to CT Image IOD specification.
            
        Raises:
            TemplateError: If pixel array dimensions are invalid, geometric parameters
                are inconsistent, or required DICOM elements cannot be created.
                
        Clinical Notes:
            The CT Image template ensures proper clinical interpretation by including:
            
            **Geometric Accuracy**: Image Position/Orientation elements maintain
            sub-millimeter accuracy critical for stereotactic treatments.
            
            **Hounsfield Unit Scaling**: Rescale Slope/Intercept elements ensure proper
            tissue density calculations for dose algorithms.
            
            **Patient Positioning**: Consistent coordinate system establishment for
            treatment delivery accuracy.
            
            **Multi-vendor Compatibility**: IOD compliance ensures files load properly
            in all major treatment planning systems.
            
        Examples:
            Create standard axial CT slice:
            
            >>> pixel_data = np.random.randint(-1000, 3000, (512, 512), dtype=np.int16)
            >>> dataset = CTImageTemplate.create_dataset(
            ...     pixel_array=pixel_data,
            ...     pixel_spacing=(1.0, 1.0),
            ...     slice_thickness=2.5,
            ...     image_position=(-255.0, -255.0, 100.0)
            ... )
            >>> print(dataset.SOPClassUID)
            1.2.840.10008.*******.1.2
            
            Create high-resolution planning CT:
            
            >>> hires_data = np.random.randint(-1000, 3000, (1024, 1024), dtype=np.int16)
            >>> dataset = CTImageTemplate.create_dataset(
            ...     pixel_array=hires_data,
            ...     pixel_spacing=(0.5, 0.5),  # High resolution
            ...     slice_thickness=1.25,       # Thin slices
            ...     image_position=(-255.0, -255.0, 50.0),
            ...     KVP=120,  # Additional CT parameters
            ...     XRayTubeCurrent=200
            ... )
            
            Create CT with custom Hounsfield scaling:
            
            >>> # For systems using different HU scaling
            >>> dataset = CTImageTemplate.create_dataset(
            ...     pixel_array=pixel_data,
            ...     pixel_spacing=(1.0, 1.0),
            ...     slice_thickness=2.5,
            ...     image_position=(-255.0, -255.0, 75.0),
            ...     rescale_intercept=-1000.0,  # Custom scaling
            ...     rescale_slope=0.5
            ... )
        """
        # Validate input parameters
        if pixel_array.ndim != 2:
            raise TemplateError(
                f"Pixel array must be 2D, got {pixel_array.ndim}D array",
                template_type="CT Image",
                suggestions=[
                    "Use 2D numpy array for single CT slice",
                    "For multi-slice data, create separate datasets for each slice",
                    "Ensure array shape is (rows, columns)"
                ],
                clinical_context={
                    "array_shape": pixel_array.shape,
                    "expected_dimensions": 2
                }
            )
            
        if len(pixel_spacing) != 2:
            raise TemplateError(
                f"Pixel spacing must have 2 elements, got {len(pixel_spacing)}",
                template_type="CT Image",
                suggestions=[
                    "Provide pixel spacing as (row_spacing, col_spacing) in mm",
                    "Typical CT pixel spacing ranges from 0.5-2.0mm",
                    "Use same value for both if pixels are square"
                ]
            )
            
        if len(image_position) != 3:
            raise TemplateError(
                f"Image position must have 3 elements (x,y,z), got {len(image_position)}",
                template_type="CT Image",
                suggestions=[
                    "Provide position as (x, y, z) coordinates in mm",
                    "Position should be in DICOM patient coordinate system",
                    "For axial images, z-coordinate represents slice location"
                ]
            )
            
        if len(image_orientation) != 6:
            raise TemplateError(
                f"Image orientation must have 6 elements, got {len(image_orientation)}",
                template_type="CT Image", 
                suggestions=[
                    "Provide orientation as 6 direction cosines",
                    "Format: (row_x, row_y, row_z, col_x, col_y, col_z)",
                    "Use (1,0,0,0,1,0) for standard axial orientation"
                ]
            )
        
        # Create base dataset
        dataset = Dataset()
        
        # SOP Common Module (C.12.1) - Required
        dataset.SOPClassUID = self.SOP_CLASS_UID
        # SOPInstanceUID will be set by BaseDicomCreator
        
        # Image Pixel Module (C.7.6.3) - Required for all images
        rows, cols = pixel_array.shape
        dataset.SamplesPerPixel = 1
        dataset.PhotometricInterpretation = "MONOCHROME2"
        dataset.Rows = rows
        dataset.Columns = cols
        dataset.BitsAllocated = 16
        dataset.BitsStored = 16
        dataset.HighBit = 15
        dataset.PixelRepresentation = 1  # Signed integers for CT (HU can be negative)
        dataset.PixelData = pixel_array.tobytes()
        
        # Image Plane Module (C.7.6.2) - Required for spatial reference
        dataset.PixelSpacing = list(pixel_spacing)
        dataset.ImageOrientationPatient = list(image_orientation)
        dataset.ImagePositionPatient = list(image_position)
        dataset.SliceThickness = slice_thickness
        
        # CT Image Module (C.8.2.1) - Required for CT modality
        dataset.Modality = "CT"
        dataset.ImageType = ["ORIGINAL", "PRIMARY", "AXIAL"]
        dataset.AcquisitionNumber = 1
        dataset.InstanceNumber = 1
        
        # Rescale for Hounsfield Units
        dataset.RescaleIntercept = rescale_intercept
        dataset.RescaleSlope = rescale_slope
        dataset.RescaleType = "HU"
        
        # Default CT acquisition parameters (can be overridden by kwargs)
        dataset.KVP = kwargs.get('KVP', 120)
        dataset.XRayTubeCurrent = kwargs.get('XRayTubeCurrent', 200)
        dataset.ExposureTime = kwargs.get('ExposureTime', 1000)
        dataset.FilterType = kwargs.get('FilterType', '')
        dataset.GeneratorPower = kwargs.get('GeneratorPower', 24000)
        
        # Reconstruction parameters
        dataset.ConvolutionKernel = kwargs.get('ConvolutionKernel', 'STANDARD')
        dataset.ReconstructionDiameter = kwargs.get(
            'ReconstructionDiameter', 
            max(rows * pixel_spacing[0], cols * pixel_spacing[1])
        )
        
        # Patient positioning (default to Head First Supine)
        dataset.PatientPosition = kwargs.get('PatientPosition', 'HFS')
        
        # Multi-frame Module elements (if applicable)
        dataset.InstanceNumber = kwargs.get('InstanceNumber', 1)
        dataset.ImageComments = kwargs.get('ImageComments', '')
        
        # Add any additional elements from kwargs
        for key, value in kwargs.items():
            if key not in ['KVP', 'XRayTubeCurrent', 'ExposureTime', 'FilterType', 
                          'GeneratorPower', 'ConvolutionKernel', 'ReconstructionDiameter',
                          'PatientPosition', 'InstanceNumber', 'ImageComments']:
                # Only add if it's a valid DICOM element
                try:
                    setattr(dataset, key, value)
                except Exception:
                    # Skip invalid DICOM elements
                    continue
        
        return dataset
        
    @classmethod
    def validate_compliance(cls, dataset: Dataset) -> list[str]:
        """Validate dataset compliance with CT Image IOD.
        
        Args:
            dataset: DICOM dataset to validate.
            
        Returns:
            List of validation error messages. Empty list indicates compliance.
            
        Clinical Notes:
            Validation ensures that created CT images will load properly in
            clinical systems and maintain geometric accuracy for treatment
            planning workflows.
        """
        errors = []
        
        # Check SOP Class UID
        if not hasattr(dataset, 'SOPClassUID'):
            errors.append("Missing required SOPClassUID")
        elif dataset.SOPClassUID != cls.SOP_CLASS_UID:
            errors.append(f"Invalid SOPClassUID: expected {cls.SOP_CLASS_UID}, got {dataset.SOPClassUID}")
            
        # Check required image elements
        required_elements = [
            'Rows', 'Columns', 'PixelData', 'PixelSpacing',
            'ImageOrientationPatient', 'ImagePositionPatient',
            'Modality', 'SliceThickness'
        ]
        
        for element in required_elements:
            if not hasattr(dataset, element):
                errors.append(f"Missing required element: {element}")
                
        # Validate CT-specific elements
        if hasattr(dataset, 'Modality') and dataset.Modality != 'CT':
            errors.append(f"Invalid modality for CT template: {dataset.Modality}")
            
        # Validate pixel representation for CT
        if hasattr(dataset, 'PixelRepresentation') and dataset.PixelRepresentation != 1:
            errors.append("CT images should use signed pixel representation for Hounsfield Units")
            
        # Validate rescale parameters
        if hasattr(dataset, 'RescaleType') and dataset.RescaleType != 'HU':
            errors.append("CT images should have RescaleType of 'HU' for Hounsfield Units")
            
        return errors