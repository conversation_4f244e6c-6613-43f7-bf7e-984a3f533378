# Phase 1 Implementation Tasks: CT Series & RT Structure Creation

**Timeline**: Months 1-2 (Weeks 3-8)  
**Objective**: Implement CT/Structure creation with integrated testing, building on completed foundation

## Prerequisites & Foundation Status

This document assumes completion of **Phase 1 Task 01** (Foundation Architecture), which established:

### ✅ Completed Foundation Components
- **Project Structure**: Complete module architecture with `src/pyrt_dicom/core/`, `utils/`, `uid_generation/`, `coordinates/`
- **Exception Hierarchy**: 6 RT-specific exception classes (`PyrtDicomError`, `DicomCreationError`, `ValidationError`, `CoordinateSystemError`, `UIDError`, `TemplateError`)
- **Clinical Logging**: JSON audit trail framework with ISO 8601 timestamps and clinical context
- **UID Generation System**: DICOM-compliant UID generation with hash-based and random strategies, comprehensive registry for Study/Series/Instance hierarchy
- **Base DICOM Creator**: Abstract `BaseDicomCreator` class with Template Method pattern, patient info handling, validation framework, and file saving
- **Coordinate System Framework**: Complete transformation system with PyMedPhys-compatible patterns, frame of reference management, and geometric validation

### Foundation Test Coverage
- **504 total tests passing** across all foundation components
- **100% coverage** for implemented modules
- **Sub-millimeter geometric accuracy** validated (<0.1mm round-trip error)
- **Real DICOM integration** with pydicom file creation and validation

## Task Breakdown Strategy

Each task includes immediate unit testing requirements. Tests are written **as functionality is implemented**, not deferred to the end. This ensures:
- Rapid feedback on API design decisions
- Regression protection as complexity grows  
- Confidence in clinical data handling from day one
- Documentation through test examples

## Week 3-4: CT Series Implementation

### Task 2.1: CT Image Series Creation
**Duration**: 5 days  
**Priority**: High (First concrete DICOM implementation)

#### Subtasks:
- [ ] **2.1.1**: Create CT template and IOD structure
  - Implement `templates/ct_template.py` with CT Image IOD requirements
  - Set up modality-specific DICOM elements following DICOM Part 3 C.8.2.1
  - Include CT-specific modules: CT Image, Multi-frame, Contrast/Bolus
  - **Test**: CT template completeness and DICOM compliance (`test_ct_template.py`)

- [ ] **2.1.2**: Implement CTSeries.from_array() class method
  - Create `core/ct_series.py` inheriting from `BaseDicomCreator`
  - Support NumPy array input with geometric metadata (pixel spacing, slice thickness, patient position)
  - Handle both single slice and multi-slice (3D) array inputs
  - **Test**: Array-to-DICOM conversion with various input sizes (`test_ct_from_array.py`)

- [ ] **2.1.3**: Add pixel spacing and slice thickness handling
  - Implement geometric metadata extraction and validation using coordinate system framework
  - Support common CT acquisition parameters (0.5-2.0mm pixel spacing, 1-10mm slice thickness)
  - Integrate with `CoordinateTransformer` for proper DICOM coordinate mapping
  - **Test**: Geometric accuracy and metadata preservation (`test_ct_geometry.py`)

- [ ] **2.1.4**: Multi-slice DICOM series creation
  - Handle 3D arrays as series of 2D DICOM files with consistent UIDs
  - Implement proper slice ordering and metadata consistency using `UIDRegistry`
  - Support Image Position Patient calculation for each slice
  - **Test**: Multi-slice series integrity and ordering (`test_ct_series.py`)

- [ ] **2.1.5**: Integration testing with clinical data
  - Test with realistic CT array sizes (512x512x200) and clinical HU ranges (-1000 to +3000)
  - Validate performance targets (<5 seconds for 200 slices)
  - Verify compatibility with DICOM viewers through pydicom round-trip testing
  - **Test**: Performance benchmarks and large dataset handling (`test_ct_performance.py`)

**Success Criteria**:
- Create valid CT series from NumPy arrays meeting DICOM CT IOD requirements
- Generated files loadable in DICOM viewers (verify with pydicom reading)
- Performance target achieved: <5 seconds for 200 slice CT series
- Geometric accuracy maintained through array-to-DICOM conversion using coordinate framework

## Month 2: RT Structure Set Implementation (Week 5-8)

### Task 2.2: Structure Set Foundation  
**Duration**: 3 days
**Priority**: High (Core RT functionality)

#### Subtasks:
- [ ] **2.2.1**: Create RT Structure template
  - Implement `templates/struct_template.py` with RTSTRUCT IOD requirements
  - Set up structure-specific DICOM elements and sequences following DICOM Part 3 C.8.8.5
  - Include RT Structure Set, ROI Contour, and Structure Set ROI modules
  - **Test**: Structure template DICOM compliance (`test_struct_template.py`)

- [ ] **2.2.2**: Implement RTStructureSet base class
  - Create `core/rt_struct.py` inheriting from `BaseDicomCreator`  
  - Set up structure storage and management framework with UID integration
  - Support multiple structures per set with proper ROI number assignment
  - **Test**: Base structure set creation and metadata (`test_struct_base.py`)

- [ ] **2.2.3**: Add structure naming and color management
  - Implement structure naming conventions and color assignment
  - Support clinical-friendly color defaults (red for PTV, blue for organs, etc.)
  - Validate structure names against common clinical conventions
  - **Test**: Naming consistency and color assignment (`test_struct_naming.py`)

**Success Criteria**:
- RT Structure Set template includes all mandatory DICOM elements
- Structure management framework supports multiple structures per set
- Color and naming system provides clinically appropriate defaults

### Task 2.3: Mask-to-Contour Conversion
**Duration**: 4 days  
**Priority**: Critical (Core algorithm for structure creation)

#### Subtasks:
- [ ] **2.3.1**: Implement basic mask-to-contour algorithm
  - Create contour extraction from binary masks using marching squares or similar
  - Support sub-pixel accuracy following clinical standards (0.1mm precision)
  - Handle edge cases: empty masks, single-pixel structures, complex topologies
  - **Test**: Contour accuracy against known geometric shapes (`test_mask_to_contour.py`)

- [ ] **2.3.2**: Add slice-by-slice contour processing
  - Handle 3D mask arrays with proper slice association to CT reference
  - Maintain coordinate system consistency across slices using `FrameOfReference`
  - Support different slice orientations (axial, sagittal, coronal)
  - **Test**: Multi-slice contour consistency (`test_slice_processing.py`)

- [ ] **2.3.3**: Optimize contour point density  
  - Implement adaptive point density based on curvature and clinical requirements
  - Balance file size vs. geometric accuracy (target: <1000 points per contour)
  - Support contour simplification while maintaining clinical accuracy
  - **Test**: Contour optimization effectiveness (`test_contour_optimization.py`)

- [ ] **2.3.4**: Add geometric validation for generated contours
  - Validate contour closure and geometric consistency using `GeometricValidator`
  - Check for self-intersections and degenerate contours
  - Ensure contours lie within CT image bounds
  - **Test**: Contour geometric validation (`test_contour_validation.py`)

**Success Criteria**:
- Mask-to-contour conversion maintains <0.5mm geometric accuracy
- Generated contours load properly in TPS systems (verify with test data)
- Contour optimization reduces file sizes while preserving clinical accuracy

### Task 2.4: RTStructureSet.from_masks() Implementation
**Duration**: 3 days
**Priority**: High (Primary API method)

#### Subtasks:
- [ ] **2.4.1**: Implement from_masks() class method
  - Create main API method accepting mask dictionary and CT reference
  - Handle multiple structures with proper DICOM sequencing and UID relationships
  - Support both 2D and 3D mask inputs with automatic detection
  - **Test**: Multi-structure creation from mask inputs (`test_from_masks.py`)

- [ ] **2.4.2**: Add structure metadata integration
  - Link structures to CT reference with proper Frame of Reference UIDs
  - Set up ROI number assignment and structure relationships using `UIDRegistry`
  - Validate geometric consistency between masks and CT reference
  - **Test**: Structure-CT reference consistency (`test_struct_reference.py`)

- [ ] **2.4.3**: Implement add_structure() method for dynamic addition
  - Support adding individual structures to existing structure sets
  - Maintain DICOM sequence consistency and UID relationships
  - Handle ROI number conflicts and automatic reassignment
  - **Test**: Dynamic structure addition (`test_add_structure.py`)

**Success Criteria**:
- from_masks() creates complete DICOM RTSTRUCT from input masks
- Structure-CT geometric relationships maintained with Frame of Reference UIDs
- Generated structure sets validate against DICOM standard

### Task 2.5: Clinical Validation Framework
**Duration**: 2 days
**Priority**: Medium (Quality assurance)

#### Subtasks:
- [ ] **2.5.1**: Implement clinical validation rules
  - Create `validation/clinical.py` with structure volume and naming validation
  - Add geometric reasonableness checks (volume limits, aspect ratios)
  - Validate against common clinical constraints and TPS requirements
  - **Test**: Clinical validation rule enforcement (`test_clinical_validation.py`)

- [ ] **2.5.2**: Add DICOM compliance validation  
  - Create `validation/dicom_compliance.py` for IOD compliance checking
  - Validate against DICOM standard requirements for CT and RTSTRUCT
  - Check mandatory elements, sequence structures, and value representations
  - **Test**: DICOM compliance verification (`test_dicom_compliance.py`)

**Success Criteria**:
- Clinical validation catches common structure creation errors
- DICOM compliance validation ensures standard conformance
- Validation provides helpful error messages with specific guidance

## Integration & Testing (Week 8)

### Task 2.6: End-to-End Integration Testing
**Duration**: 2 days
**Priority**: Critical (Validation of complete workflow)

#### Subtasks:
- [ ] **2.6.1**: Complete workflow testing
  - Test CT + Structure creation pipeline with realistic clinical data
  - Validate file compatibility with external DICOM tools (pydicom, DICOM validators)
  - Test complete RT workflow scenarios (planning CT with multiple structures)
  - **Test**: Complete RT workflow validation (`test_integration_workflow.py`)

- [ ] **2.6.2**: Performance benchmark validation
  - Confirm all performance targets met with clinical-scale datasets  
  - Profile memory usage and identify optimization opportunities
  - Test with large datasets (512x512x400 CT, 20+ structures)
  - **Test**: Performance benchmark suite (`test_performance_benchmarks.py`)

- [ ] **2.6.3**: Cross-platform compatibility testing
  - Test on multiple Python versions (3.10+)
  - Validate across different operating systems if possible
  - Ensure consistent behavior with different NumPy/pydicom versions
  - **Test**: Cross-platform compatibility (`test_cross_platform.py`)

**Success Criteria**:
- Complete CT + Structure workflow executes successfully with clinical data
- All performance targets achieved under realistic conditions
- Generated DICOM files validate with external tools (pydicom, DICOM validators)

## Testing Strategy Throughout Phase 1 Task 02

### Unit Testing Approach
- **Test-First Development**: Write tests as functionality is implemented
- **Clinical Data Focus**: Use realistic data sizes and ranges in tests
- **Comprehensive Coverage**: Target >95% code coverage for core functionality
- **Performance Integration**: Include performance assertions in relevant tests

### Test Data Management
- Create fixture datasets representing typical clinical scenarios
- Include edge cases (small/large structures, various CT geometries)
- Maintain test data versioning for regression testing
- Use synthetic data to avoid PHI concerns while maintaining clinical realism

### Continuous Integration
- Run full test suite on every commit
- Include performance regression testing
- Validate DICOM compliance in automated testing
- Maintain compatibility with foundation components

## Dependencies & Prerequisites

### External Dependencies
- `pydicom>=3.0.1` (DICOM handling)
- `numpy` (array operations)  
- `pytest` (testing framework)
- `coverage` (test coverage analysis)
- `scipy` (for contour processing algorithms)

### Internal Dependencies (From Foundation)
- `BaseDicomCreator` class (Task 1.3) - Required for CT and Structure inheritance
- `UIDGenerator` and `UIDRegistry` (Task 1.2) - Required for DICOM UID management
- `CoordinateTransformer` and `FrameOfReference` (Task 1.4) - Required for geometric accuracy
- Exception hierarchy and clinical logging (Task 1.1) - Required for error handling and audit trails

### Task Dependencies Within Phase 1 Task 02
- CT template (2.1.1) required before CT series implementation (2.1.2-2.1.5)
- Structure template (2.2.1) required before structure implementation (2.2.2-2.2.3)
- Mask-to-contour conversion (2.3) required before from_masks() implementation (2.4)
- All core functionality (2.1-2.4) required before validation framework (2.5)
- Complete implementation required before integration testing (2.6)

## Success Metrics for Phase 1 Task 02

### Functional Requirements
- **Create valid CT series from NumPy arrays (<5 seconds for 200 slices)**: Performance target for clinical workflow integration
- **Generate RT Structure Sets that load in DICOM viewers**: Compatibility validation with external tools
- **Pass DICOM compliance validation for CT and RTSTRUCT**: Standard conformance verification
- **Handle coordinate transformations with <1mm accuracy**: Geometric precision using foundation coordinate framework
- **Support clinical-scale datasets**: 512x512x400 CT volumes, 20+ structures per set

### Quality Requirements
- **>95% unit test coverage for implemented functionality**: Comprehensive testing of all new components
- **No critical validation failures in clinical range tests**: Robust handling of realistic clinical data
- **Generated files compatible with pydicom reading/writing**: Round-trip validation and external tool compatibility
- **Comprehensive error handling with helpful clinical guidance**: User-friendly error messages with clinical context
- **Code follows established patterns from foundation**: Consistency with BaseDicomCreator and coordinate framework

### Performance Requirements
- **CT creation: <5 seconds for 200-slice series**: Clinical workflow performance target
- **Structure creation: <3 seconds for 20 structures**: Multi-structure processing efficiency
- **Memory usage: <1GB for typical clinical datasets**: Resource efficiency for clinical workstations
- **All operations complete within performance targets under unit testing**: Automated performance validation

### Integration Requirements
- **Complete CT + Structure workflow executes successfully**: End-to-end validation with clinical data
- **Foundation components properly utilized**: Effective use of UID generation, coordinate systems, validation
- **External tool compatibility verified**: DICOM viewer loading, pydicom round-trip validation
- **Cross-platform compatibility maintained**: Consistent behavior across Python versions and operating systems

## Implementation Notes

### Code Organization
- Follow established patterns from foundation components
- Use Template Method pattern inheritance from `BaseDicomCreator`
- Integrate with existing UID generation and coordinate systems
- Maintain clinical logging and error handling consistency

### Clinical Considerations
- Support common CT acquisition parameters and orientations
- Handle typical structure naming conventions and color schemes
- Validate against clinical constraints and TPS requirements
- Provide helpful error messages with clinical context

### Performance Optimization
- Efficient NumPy array processing for large datasets
- Optimized contour generation algorithms
- Memory-efficient multi-slice processing
- Parallel processing opportunities where appropriate

This document provides a complete, self-contained specification for implementing CT series and RT structure creation functionality, building on the solid foundation established in Phase 1 Task 01.
