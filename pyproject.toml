[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "pyrt_dicom"
description = "Complement existing tools (PyMedPhys for analysis, scikit-rt for research) by becoming the go-to library for DICOM RT file creation"
readme = "README.md"
authors = [
  {name = "<PERSON>", email = "<EMAIL>"}
]
maintainers = [
  {name = "<PERSON>", email = "<EMAIL>"}
]
classifiers = [

]
license = {text = "MIT"}
dependencies = [
  "numpy>=2.2.6",
  "pydicom>=3.0.1",
  "scikit-rt>=0.3.19",
  "typer",
]
requires-python = ">= 3.10"

dynamic = ["version"]

[project.optional-dependencies]
test = [
    "coverage",  # testing
    "pytest",  # testing
    "hypothesis>=6.0",  # property-based testing
    "ruff",  # linting
    "ty", # checking types
]

[project.urls]

bugs = "https://github.com/srobertson86/pyrt_dicom/issues"
changelog = "https://github.com/srobertson86/pyrt_dicom/blob/master/changelog.md"
homepage = "https://github.com/srobertson86/pyrt_dicom"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.package-data]
"*" = ["*.*"]

[project.scripts]
pyrt_dicom = "pyrt_dicom.cli:app"

[tool.setuptools.dynamic]
version = {attr = "pyrt_dicom.__version__"}

[tool.ty]
# All rules are enabled as "error" by default; no need to specify unless overriding.
# Example override: relax a rule for the entire project (uncomment if needed).
# rules.TY015 = "warn"  # For invalid-argument-type, warn instead of error.

# Optional: Per-directory overrides for flexibility in boilerplates with tests or examples.
[[tool.ty.overrides]]
match = ["tests/**.py"]
rules.TY016 = "ignore"  # Ignore invalid-assignment in tests, e.g., for dynamic fixtures.
rules.TY029 = "ignore"  # Ignore invalid-return-type in tests.

[tool.ruff]
line-length = 120

[tool.ruff.lint]
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # Pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "UP",  # pyupgrade
]

[dependency-groups]
dev = [
    "black>=25.1.0",
    "mypy>=1.17.0",
    "pytest>=8.4.1",
    "pytest-cov>=6.2.1",
    "ruff>=0.12.4",
    "sphinx>=8.1.3",
]
